import uuid
from operator import index

from sqlalchemy.dialects.postgresql import UUID, JSONB
from datetime import datetime
from pgvector.sqlalchemy import Vector
from sqlalchemy import Index
from models import db, ma
from models.enums import Sentiment


# Represents a user conversation, including metadata such as the user, tenant, and module involved.
class Conversation(db.Model):
    __tablename__ = 'conversations'
    id = db.Column(db.String(36), primary_key=True)
    user_id = db.Column(db.String(36), nullable=False)
    tenant_name = db.Column(db.String(36), nullable=False)
    first_message = db.Column(db.String)
    module_identifier = db.Column(db.String(36), nullable=False)
    entity_type = db.Column(db.String(36), nullable=True)
    entity_id = db.Column(db.String(36), nullable=True)
    entity_name = db.Column(db.String(36), nullable=True)
    createdAt = db.Column(db.DateTime, default=datetime.utcnow)
    lastUpdatedAt = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Represents a few-shot example for AI models, including embeddings for similarity searches.
class FewShotExample(db.Model):
    __tablename__ = 'few_shot_examples'

    tenant_name = db.Column(db.String(36), primary_key=True, nullable=False)
    question = db.Column(db.Text, primary_key=True, nullable=False)
    expected_trino_query = db.Column(db.Text)
    explanation = db.Column(db.Text)

    # CREATE EXTENSION IF NOT EXISTS vector;
    # For text-embedding-3-small - vector size = 1536
    # For text-embedding-3-large - vector size = 3072
    question_embedding = db.Column(Vector(1536))

    __table_args__ = (
        Index('ix_few_shot_examples_question_embedding', question_embedding,
              postgresql_using='hnsw',
              postgresql_ops={'question_embedding': 'vector_cosine_ops'},
              postgresql_with={'m': 16, 'ef_construction': 64}),
    )


# Represents a template for AI-generated answers, including embeddings for matching questions.
class AnswerTemplate(db.Model):
    __tablename__ = 'answer_templates'

    intent_classification = db.Column(db.String(36), nullable=False)
    feature = db.Column(db.Text, primary_key=True, nullable=False)
    question_template = db.Column(db.Text, nullable=False)
    response_template = db.Column(db.Text)
    explanation = db.Column(db.Text)

    # For text-embedding-3-small - vector size = 1536
    # For text-embedding-3-large - vector size = 3072
    question_template_embedding = db.Column(Vector(1536))

    __table_args__ = (
        Index('ix_answer_templates_question_template_embedding', question_template_embedding,
              postgresql_using='hnsw',
              postgresql_ops={'question_template_embedding': 'vector_cosine_ops'},
              postgresql_with={'m': 16, 'ef_construction': 64}),
    )


# Represents user feedback for conversations, including sentiment analysis and comments.
class UserFeedBack(db.Model):
    __tablename__ = 'user_feedback'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), nullable=False)
    tenant_name = db.Column(db.String(36), nullable=False)
    conversation_id = db.Column(db.String(36), nullable=False)
    sentiment = db.Column(
        db.Enum(Sentiment, name='sentiment_enum', create_type=False),
        nullable=False
    )
    comment = db.Column(db.Text, nullable=True)
    question = db.Column(db.Text, nullable=False)
    answer = db.Column(db.Text, nullable=False)
    createdAt = db.Column(db.DateTime, default=datetime.utcnow)
    lastUpdatedAt = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Represents questions asked by users during a conversation.
class UserQuestion(db.Model):
    __tablename__ = 'user_question'
    id = db.Column(db.String(36), primary_key=True)
    user_id = db.Column(db.String(36), nullable=False)
    conversation_id = db.Column(db.String(36), nullable=False)
    question = db.Column(db.Text, nullable=False)
    module_identifier = db.Column(db.String, nullable=False)
    created_on = db.Column(db.DateTime, default=datetime.utcnow)


class ConversationHistory(db.Model):
    __tablename__ = 'Conversation_History'
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.UUID(as_uuid=True), nullable=False, default=uuid.uuid4, index=True)
    message = db.Column(JSONB, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)


class ConversationHistorySchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ConversationHistory
        load_instance = True


# Schemas for serialization and validation
class ConversationSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = Conversation
        load_instance = True


class UserFeedbackSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = UserFeedBack
        load_instance = True


class UserQuestionSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = UserQuestion
        load_instance = True
