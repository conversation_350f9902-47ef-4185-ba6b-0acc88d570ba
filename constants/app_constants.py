# Headers
USER_ID_HEADER = "User-Id"
TENANT_NAME_HEADER = "Tenant-Name"

# OpenAI Cookbook (Tiktoken)
MAX_ALLOWED_TOKENS = 10000  ## Maximum allowable tokens
OPENAI_MODEL = "gpt-4o-mini"  ## OpenAI model for tiktoken:
ENCODING_NAME = "o200k_base"  ## Encoding name specify how text is converted into tokens.

# Query constants for Distributed lock on ongoing conversation
CHECK_LOCK_QUERY = "SELECT pg_try_advisory_lock(hashtext(%s))"  # Query to check and acquire lock
RELEASE_LOCK_QUERY = "SELECT pg_advisory_unlock(hashtext(%s))"  # Query to release lock
