import psycopg2
from psycopg2.extras import RealDictCursor

from utils.JsonUtils import json_serializable


def execute_query(datasource_url, database_name, table_name, query):
    #TODO - Hack as AI is not providing this. Please remove this
    #datasource_url = "postgresql://hull_np_read_user:1AQ6^<EMAIL>:5432/questmarine"
    # datasource_url = "postgresql://hull_np_read_user:1AQ6^oo3u8Ow@localhost:5435/questmarine"
    datasource_url="postgresql://postgres:postgres@127.0.0.1:5432/questmarine"
    conn = psycopg2.connect(datasource_url)
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    cursor.execute(query)
    results = cursor.fetchall()
    cursor.close()
    conn.close()

    # Serialize the results to JSON
    serializable_results = [json_serializable(result) for result in results]

    return serializable_results