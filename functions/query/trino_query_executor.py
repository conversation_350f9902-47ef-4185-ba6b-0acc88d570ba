import re
from contextlib import contextmanager

import pandas as pd
import trino
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
from trino.dbapi import connect

from config.app_config import Config
from utils.LogUtils import LOG
class TrinoConnectionPool:
    def __init__(self, host, port, user, pool_size=5, max_overflow=10, timeout=30):
        self.connection_params = {
            "host": host,
            "port": port,
            "user": user,
        }
        self.engine = create_engine(
            f"trino://{host}:{port}",
            creator=self._create_connection,
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=timeout
        )

    def _create_connection(self):
        return connect(**self.connection_params)

    @contextmanager
    def get_connection(self):
        conn = self.engine.raw_connection()
        try:
            yield conn
        finally:
            conn.close()



pool = TrinoConnectionPool(
        host=Config.TRINO_HOST,
        port=Config.TRINO_PORT,
        user="trino"
)

def execute_query(query):
    LOG.info(f"Executing trino query on {Config.TRINO_HOST} and port = {Config.TRINO_PORT}")

    try:
        with pool.get_connection() as conn:
            sanitized_query = query.strip().rstrip(';')
            cursor = conn.cursor()
            cursor.execute(sanitized_query)
            results = cursor.fetchall()

            df = pd.DataFrame(results, columns=[desc[0] for desc in cursor.description])
            json_result = df.to_json(orient='records', date_format='iso', indent=2)
            return json_result
    except Exception as e:
        LOG.error(f"An error occurred: {str(e)}")
        return {"result": "query failed. Try a different query"}
    finally:
        conn.close()

def sanitize_query(query):
    """
    Sanitize the input query.

    :param query: Input SQL query
    :return: Sanitized SQL query
    """
    # Remove any trailing semicolons
    query = query.strip().rstrip(';')

    # Remove any SQL comments
    query = re.sub(r'--.*$', '', query, flags=re.MULTILINE)
    query = re.sub(r'/\*[\s\S]*?\*/', '', query)

    # Replace multiple spaces with a single space
    query = re.sub(r'\s+', ' ', query)

    sanitized_query = query.strip()
    LOG.info(f"The sanitized TRINO QUERY - {sanitized_query}")
    return sanitized_query
